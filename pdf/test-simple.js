// 简单的服务端测试
const http = require('http');

const testData = {
    leftImageData: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    rightImageData: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    filename: '第二册 面积_split.pdf'
};

function testServer() {
    const postData = JSON.stringify(testData);
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/split-pdf-images',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };

    console.log('🧪 测试服务端PDF拆分...');
    console.log('文件名:', testData.filename);

    const req = http.request(options, (res) => {
        console.log('状态码:', res.statusCode);
        console.log('响应头:', res.headers);
        
        let data = Buffer.alloc(0);
        res.on('data', chunk => data = Buffer.concat([data, chunk]));
        res.on('end', () => {
            if (res.statusCode === 200) {
                console.log('✅ 成功! 文件大小:', data.length, 'bytes');
                console.log('📁 文件名:', res.headers['content-disposition']);
            } else {
                console.log('❌ 失败:', data.toString());
            }
        });
    });

    req.on('error', (error) => {
        console.error('❌ 请求错误:', error.message);
    });

    req.write(postData);
    req.end();
}

// 等待服务器启动
setTimeout(testServer, 2000);
